# 目的:
# 描述:常用函数

# 导出结果为Project下的output文件夹,可以直接修改
# 2 flextable导出------------------------------------------------------
save_flextable_as_docx <- function(table, filename,orient = 'landscape') {
  library(flextable)
  library(officer)
  pacman::p_load(flextable)
  # 指定保存的路径
  output_path <- paste0(here(), "/output")
  # 创建完整的文件路径
  file_path <- file.path(output_path, paste0(filename, ".docx"))
  # word 表格方向和页面大小
  sect_properties <- prop_section(
    page_size = page_size(orient = orient),
    type = "continuous")
  # 保存表格为 Word 文档
  table |>
    as_flex_table() |>
    flextable::save_as_docx(path = file_path,pr_section = sect_properties)
}

# 导出结果为Project下的figure文件夹,可以直接修改
save_ggplot <- function(plot, filename) {
  # 指定保存的路径
  output_path <- paste0(here(),'/figure')
  # 创建完整的文件路径
  file_path <- file.path(output_path, paste0(filename, ".pdf"))
  # 保存图表
  ggsave(file_path, plot = plot,  units = "in",width = 11.68, height = 8.27, dpi = 300)
}

save_png <- function(plot, filename,width = 8, height = 6, dpi = 72) {
  library(ggplot2)
  # 指定保存的路径
  output_path <- paste0(here(),'/figure')
  # 创建完整的文件路径
  file_path <- file.path(output_path, paste0(filename, ".png"))
  # 保存图表
  ggsave(file_path, plot = plot,  units = "in",width = width, height = height, dpi = dpi)
}

# 3 拼接变量集合 -------------------------------------------------


paste_data_vars <- function(data) {
  paste0("'", paste(data |> colnames(), collapse = "','"), "'")
}


# 4. 识别分类变量 ---------------------------------------------------------------

select_fct_vars <-
  function(data, level) {
    data |>
      summarise_all(n_distinct) |>
      select_if(~ all(. <= level)) |>
      paste_data_vars()
  }

